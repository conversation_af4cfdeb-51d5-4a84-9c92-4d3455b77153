# 发帖询问

## 标题:
DLL函数参数问题: pfINa是输入的收盘价, pfINa[0]表示最近一天的收盘价? 还是最远一天的收盘价?


DLL函数参数问题: pfINa是输入的收盘价, pfINa[0]表示最近一天的收盘价? 还是最远一天的收盘价?
函数模板示例:
void TestPlugin1(int DataLen,float* pfOUT,float* pfINa,float* pfINb,float* pfINc)


```shell

N:=200;
INC_PERCENT_MIN:=IF(INBLOCK('科创板') OR INBLOCK('创业板'),18.55,9.55)/100;
DAY_CONDITION:= (C/REF(C,1)-1)>=INC_PERCENT_MIN;
INC3_CONDITION:=EVERY(DAY_CONDITION, 3);
XG:COUNT(INC3_CONDITION, N)>=1;

```


```shell
{收盘价在200日均线±5%区间选股公式}

MA200 := MA(CLOSE, 200);
RATIO := CLOSE / MA200;
XG: RANGE(RATIO, 0.945, 1.055);

```

```shell
{紫色/橙色K线, 带MA均线的...}
MA1:MA(CLOSE,M1);
MA2:MA(CLOSE,M2);
MA3:MA(CLOSE,M3);
MA4:MA(CLOSE,M4);
MA5:MA(CLOSE,M5);
MA6:MA(CLOSE,M6);
MA7:MA(CLOSE,M7);
MA8:MA(CLOSE,M8);


NO_BEI:= NOT(CODELIKE('83') OR CODELIKE('87') OR INBLOCK('北证A股'));
NO_ST:= NOT(NAMELIKE('ST') OR NAMELIKE('*ST'));
BAN_SOME:= NO_BEI AND NO_ST;

INC_NUM := IF(INBLOCK('科创板') OR INBLOCK('创业板'), 18.85, 9.85)/100;
LAST_CLOSE := REF(CLOSE, 1);
INC_CON := (CLOSE - LAST_CLOSE)/LAST_CLOSE >= INC_NUM AND CLOSE > LAST_CLOSE AND PERIOD = 5 AND BAN_SOME;
STICKLINE(INC_CON, HIGH, LOW, 0, 0), COLORFF00FF;
STICKLINE(INC_CON, OPEN, CLOSE, 3, 0), COLORFF00FF;

DEC_NUM := IF(INBLOCK('科创板') OR INBLOCK('创业板'), 15.85, 9.85)/100;
DEC_CON := (OPEN - CLOSE)/OPEN >= DEC_NUM AND CLOSE < OPEN AND PERIOD = 5;
STICKLINE(DEC_CON, HIGH, LOW, 0, 0), COLOR0080FF;
STICKLINE(DEC_CON, OPEN, CLOSE, 3, 0), COLOR0080FF;
```
