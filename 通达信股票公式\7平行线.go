
MY_COND := (DATE<MACHINEDATE OR (DATE=MACHINEDATE AND TIME2 > 153000));

C_LAST_H := REF(HHV(H,DAYBARPOS), DAYBARPOS);
C_CUR_H := HHV(H,<PERSON>AYBARPOS);
C_H := IF(MY_COND, C_CUR_H, C_LAST_H);

C_LAST_L :=  REF(LLV(L,DAYBARPOS), DAYBARPOS);
C_CUR_L := LLV(L,DAYBARPOS);
C_L := IF(MY_COND, C_CUR_L, C_LAST_L);

C_LAST_C := REF(C, DAYBARPOS);
C_CUR_C := C;
C_C := IF(MY_COND, C_CUR_C, C_LAST_C);

C_X := (C_H + C_L + C_C) / 3;
//DRAWSL(ISLASTBAR, C_H,0,1000,1,COLORGREEN);
//DRAWSL(ISLASTBAR, C_L,0,1000,1,COLORRED);

// 轴心位
DRAWSL(ISLASTBAR, C_X, 0,1000,1,COLORYELLOW);
DRAWTEXT(ISLASTBAR, C_X , 'O  '), COLORYELLOW, ALIGN2, VALIGN0;
DRAWNUMBER(ISLASTBAR, C_X, C_X, 2, COLORYELLOW), ALIGN0, VALIGN0;

// 高位1
H1 :=  2*C_X - C_L;
DRAWSL(ISLASTBAR, H1, 0,1000,1,COLORGREEN);
DRAWTEXT(ISLASTBAR, H1 , 'H1 '), COLORGREEN, ALIGN2, VALIGN0;
DRAWNUMBER(ISLASTBAR, H1, H1, 2, COLORGREEN), ALIGN0, VALIGN0;
//低位1
L1 := 2*C_X - C_H;
DRAWSL(ISLASTBAR, L1, 0,1000,1,COLORRED);
DRAWTEXT(ISLASTBAR, L1 , 'L1 '), COLORRED, ALIGN2, VALIGN0;
DRAWNUMBER(ISLASTBAR, L1, L1, 2, COLORRED), ALIGN0, VALIGN0;

// 高位2
H2 := C_X + (C_H - C_L);
DRAWSL(ISLASTBAR, H2, 0,1000,1,COLORGREEN);
DRAWTEXT(ISLASTBAR, H2 , 'H2 '), COLORGREEN, ALIGN2, VALIGN0;
DRAWNUMBER(ISLASTBAR, H2, H2, 2, COLORGREEN), ALIGN0, VALIGN0;
// 低位2
L2 := C_X - (C_H - C_L);
DRAWSL(ISLASTBAR, L2, 0,1000,1,COLORRED);
DRAWTEXT(ISLASTBAR, L2 , 'L2 '), COLORRED, ALIGN2, VALIGN0;
DRAWNUMBER(ISLASTBAR, L2, L2, 2, COLORRED), ALIGN0, VALIGN0;

// 高位3
H3 := H1 + (C_H - C_L);
DRAWSL(ISLASTBAR, H3, 0,1000,1,COLORGREEN);
DRAWTEXT(ISLASTBAR, H3 , 'H3 '), COLORGREEN, ALIGN2, VALIGN0;
DRAWNUMBER(ISLASTBAR, H3, H3, 2, COLORGREEN), ALIGN0, VALIGN0;
// 低位3
L3 := L1 - (C_H - C_L);
DRAWSL(ISLASTBAR, L3, 0,1000,1,COLORRED);
DRAWTEXT(ISLASTBAR, L3 , 'L3 '), COLORRED, ALIGN2, VALIGN0;
DRAWNUMBER(ISLASTBAR, L3, L3, 2, COLORRED), ALIGN0, VALIGN0;
